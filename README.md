# Assistant WebSocket Schema

Shared TypeScript types for the TwoShot Assistant WebSocket API.

## Usage

This repository is intended to be used as a git submodule in both the frontend and backend projects.

### Adding as a submodule

```bash
git submodule add https://github.com/twoshotapp/assistant-websocket-schema.git src/shared/assistant-websocket-schema
```

### Importing types

```typescript
import { StructuredUserMessage, AudioReference } from './shared/assistant-websocket-schema/structured-message';
import { AudioId, SampleId } from '../model/id';

// Use with your branded types
type MyStructuredMessage = StructuredUserMessage<AudioId, SampleId>;
```

## Types

- `AudioReference<AudioIdType, SampleIdType>` - Reference to an audio file with optional sample
- `MessageContentPart<AudioIdType, SampleIdType>` - Text or audio reference
- `StructuredUserMessage<AudioIdType, SampleIdType>` - Complete message structure
- `StructuredMessagePayload<AudioIdType, SampleIdType>` - WebSocket payload wrapper
import React, {<PERSON>actN<PERSON>, useCallback} from "react";
import {useLocation, useSearchParams} from "react-router-dom";
import {
    GetGenerationJobsRequest,
    GetKitsRequest,
    GetKitsSortEnum,
    GetProjectsRequest,
    GetProjectsSortEnum,
    GetSamplesRequest,
    GetSamplesSortEnum,
    ModelGetRequest,
    ModelSort,
    QueryUsersRequest
} from "../../gen";
import {
    useGetArrayQueryParam,
    useGetBooleanQueryParam,
    useGetFlagQueryParam,
    useGetFloatQueryParam,
    useGetNumberQueryParam,
    useGetQueryParam,
    useQueryParams
} from "../ParamUtils";
import {usePacksSort} from "../page/app/Packs";
import {AiIcon, CloseIcon, Icon, IconProp, MaterialIcon} from "./icons";
import {AddInput} from "./AddInput";
import {useIsFreeTier} from "./subscription";
import {LinkState} from "./LinkState";
import {useIsLoggedIn} from "../hooks/UseIsLoggedIn";
import {useNavigateWithPluginState} from "../hooks/navigate";
import {SearchValueComponentRaw} from "./SearchValueComponentRaw";
import {usePageContext} from "../hooks/PageContext";
import {capitalize, formatBPM, formatSeconds, isOneOf} from "../utils";
import {useAuthContext} from "../hooks/auth";
import classNames from "classnames";
import {UserId} from "../models";
import {
    baseExploreQueryParams,
    baseModelQueryParams,
    baseSampleQueryParams,
    canExploreAudio,
    exploreRoot,
    modelRoot,
    sampleRoot,
    studioProjectName
} from "../whitelabel";
import {mergeTags} from "./ExploreQueryHeader";

function getSampleSearchParams(samplesQuery: GetSamplesRequest) {
    const params = new URLSearchParams();
    if (samplesQuery.nameRegex)
        params.append("regex", samplesQuery.nameRegex);
    if (samplesQuery.tag && samplesQuery.tag.length)
        params.append("tag", samplesQuery.tag.join(","));
    if (samplesQuery.bpmMin)
        params.append("bpmMin", samplesQuery.bpmMin.toString());
    if (samplesQuery.bpmMax)
        params.append("bpmMax", samplesQuery.bpmMax.toString());
    if (samplesQuery.durationMin)
        params.append("durationMin", samplesQuery.durationMin.toString());
    if (samplesQuery.durationMax)
        params.append("durationMax", samplesQuery.durationMax.toString());
    if (samplesQuery.scale)
        params.append("scale", samplesQuery.scale);
    if (samplesQuery.royaltyFree)
        params.append("royalty-free", "true");
    if (samplesQuery.loop !== undefined)
        params.append("loop", String(samplesQuery.loop));
    if (samplesQuery.ownerVerified)
        params.append("verified", "true");
    if (samplesQuery.ownerFeatured)
        params.append("featured", "true");
    if (samplesQuery.aiGenerated !== undefined)
        params.append("ai", String(samplesQuery.aiGenerated));
    if (samplesQuery.liked)
        params.append("liked", "true");
    if (samplesQuery.free)
        params.append("free", "true");
    if (samplesQuery.sort?.length)
        params.append("sort", samplesQuery.sort.map(s => s.toLowerCase()).join(","));
    if (samplesQuery.artist)
        params.append("artist", samplesQuery.artist.toString());
    if (samplesQuery.queryString)
        params.append("q", samplesQuery.queryString);
    if (samplesQuery.negativeTag)
        params.append("negativeTag", samplesQuery.negativeTag.join(","));
    if (samplesQuery.modelId)
        params.append("model", samplesQuery.modelId.toString());

    if (samplesQuery.ownerFollowed)
        params.append("followed", "true");

    if (samplesQuery.description)
        params.append("d", samplesQuery.description);

    if (samplesQuery.single)
        params.append("single", "true");

    if (samplesQuery.folderId)
        params.append("folderId", samplesQuery.folderId.toString());


    return params;
}

const PREFIX = "similar:";

export function isSimilarSort(sort: GetSamplesSortEnum) {
    return sort.startsWith(PREFIX);
}

export function getSimilarSortValue(sort: GetSamplesSortEnum) {
    return sort.slice(PREFIX.length);
}

export function isSimilarSorted(sort: GetSamplesSortEnum[]) {
    return sort.some(isSimilarSort);
}

export function getShortSampleQueryDescription(queryParams: GetSamplesRequest) {
    const sort = queryParams.sort?.[0];

    let prompt = ""
    const {queryString, scale, liked, loop, aiGenerated, royaltyFree, tag} = queryParams

    if (sort) {
        if (sort === GetSamplesSortEnum.Random)
            prompt += "random "
        else if (sort === GetSamplesSortEnum.Latest)
            prompt += "the latest "
        else if (sort === GetSamplesSortEnum.Oldest)
            prompt += "old "
        else if (sort === GetSamplesSortEnum.Popular)
            prompt += "popular "
        else if (sort === GetSamplesSortEnum.RecentlyUsed) {
            prompt += "your recently used "
            if (liked) {
                prompt += "liked "
            }
        } else if (sort === GetSamplesSortEnum.MostUsed)
            prompt += "your most used "
        else if (isSimilarSort(sort))
            prompt += "similar "
    } else if (liked) {
        prompt += "your liked "
    }


    if (royaltyFree === true) {
        prompt += "royalty-free "
    }
    if (aiGenerated) {
        prompt += "AI-generated "
    }
    if (scale !== undefined) {
        prompt += `${scale} `
    }
    const displayTags = tag?.filter(t => t !== "sample")
    if (displayTags && displayTags.length > 0) {
        prompt += displayTags[0] + " "
    } else if (queryParams.negativeTag !== undefined) {
        prompt += "non-" + queryParams.negativeTag[0] + " "
    } else if (queryString) {
        prompt += queryString + " "
    }
    prompt += loop === undefined ?
        "samples" : loop ?
            "loops" : "one-shots"

    // if (sort === GetSamplesSortEnum.ForYou) prompt += " for you"
    return prompt;
}

export function getSampleQueryDescription(queryParams: GetSamplesRequest) {
    let shortPrompt = getShortSampleQueryDescription(queryParams);

    const {bpmMin, bpmMax, durationMin, durationMax} = queryParams;
    const bpmMinF = bpmMin ? formatBPM(bpmMin) : null;
    const bpmMaxF = bpmMax ? formatBPM(bpmMax) : null;
    const durationMinF = durationMin ? formatSeconds(durationMin) : null;
    const durationMaxF = durationMax ? formatSeconds(durationMax) : null;

    if (bpmMaxF) {
        if (bpmMinF) {
            if (bpmMinF === bpmMaxF) {
                shortPrompt += ` at ${bpmMinF} BPM`;
            } else {
                shortPrompt += ` between ${bpmMinF} and ${bpmMaxF} BPM`;
            }
        } else {
            shortPrompt += ` below ${bpmMaxF} BPM`;
        }
    } else if (bpmMinF) {
        shortPrompt += ` above ${bpmMinF} BPM`;
    }
    if ((durationMaxF || durationMinF) && (bpmMaxF || bpmMinF))
        shortPrompt += " and";
    if (durationMinF) {
        if (durationMaxF) {
            if (durationMinF === durationMaxF) {
                shortPrompt += ` ${durationMinF} long`;
            } else {
                shortPrompt += ` between ${durationMinF} and ${durationMaxF} long`;
            }
        } else {
            shortPrompt += ` above ${durationMinF}`;
        }
    } else if (durationMaxF) {
        shortPrompt += ` below ${durationMaxF}`;
    }

    // Append a phrase if filtering by followed artists
    if (queryParams.ownerFollowed) {
        shortPrompt += " from people you follow";
    }

    return shortPrompt;
}

/**
 * Optional helper to convert your enum sort into a friendly label
 */
function getProjectSortAdjective(sort?: GetProjectsSortEnum): string | null {
    switch (sort) {
        case GetProjectsSortEnum.Newest:
            return "new";
        case GetProjectsSortEnum.Oldest:
            return "old";
        case GetProjectsSortEnum.Popular:
            return "popular";
        case GetProjectsSortEnum.Random:
            return "random";
        case GetProjectsSortEnum.Updated:
            return "your recent";
        default:
            return null;
    }
}

/**
 * Creates a short, user-friendly description of the project query parameters.
 *
 * Examples of resulting strings:
 * - "the latest public projects matching \"chill\" with BPM from 100 to 120 and durations from 0:30 to 2:00"
 * - "old projects matching \"Rock Demo\""
 * - "popular public projects with BPM above 130"
 */
export function getProjectQueryDescription(queryParams: GetProjectsRequest): string {
    const {
        _public,
        name,
        sort,
        minTempo,
        maxTempo,
        minDuration,
        maxDuration
    } = queryParams;

    const sortAdjective = getProjectSortAdjective(sort);

    let prompt = "";

    if (sortAdjective) {
        prompt += sortAdjective + " ";
    }

    if (_public) {
        prompt += "shared ";
    }

    prompt += `Studio ${studioProjectName}s`;

    if (name) {
        prompt += ` matching "${name}"`;
    }

    const constraints: string[] = [];

    if (minTempo !== undefined && maxTempo !== undefined) {
        if (minTempo === maxTempo) {
            constraints.push(`BPM at ${formatBPM(minTempo)}`);
        } else {
            constraints.push(`BPM from ${formatBPM(minTempo)} to ${formatBPM(maxTempo)}`);
        }
    } else if (minTempo !== undefined) {
        constraints.push(`BPM above ${formatBPM(minTempo)}`);
    } else if (maxTempo !== undefined) {
        constraints.push(`BPM below ${formatBPM(maxTempo)}`);
    }

    if (minDuration !== undefined && maxDuration !== undefined) {
        if (minDuration === maxDuration) {
            constraints.push(`duration of ${formatSeconds(minDuration)}`);
        } else {
            constraints.push(`durations from ${formatSeconds(minDuration)} to ${formatSeconds(maxDuration)}`);
        }
    } else if (minDuration !== undefined) {
        constraints.push(`durations above ${formatSeconds(minDuration)}`);
    } else if (maxDuration !== undefined) {
        constraints.push(`durations below ${formatSeconds(maxDuration)}`);
    }

    if (constraints.length > 0) {
        prompt += " with " + constraints.join(" and ");
    }

    // Append followed flag description if set
    if (queryParams.followed) {
        prompt += " from people you follow";
    }

    return prompt.trim();
}

export function getSamplesPath(samplesQuery: GetSamplesRequest) {
    const similarSort = samplesQuery.sort?.find(isSimilarSort);
    const similarSortSampleId = similarSort ? getSimilarSortValue(similarSort) : undefined;

    const baseUrl =
        samplesQuery.artist ?
            `/artist/${samplesQuery.artist}${sampleRoot}` :
            similarSortSampleId ?
                `/sounds/${similarSortSampleId}` :
                sampleRoot;

    const params = getSampleSearchParams({
        ...samplesQuery,
        sort: samplesQuery.sort?.filter(s => !isSimilarSort(s))
    });
    return `${baseUrl}?${params}`;
}

export const SEARCH_PATH = "/coproducer";

export function useSearchWindowState() {
    // const [hash, setHash] = useHash();
    const location = useLocation();
    const navigateState = useNavigateWithPluginState();
    const {setAssistantSidebarOpen, assistantSidebarOpen} = usePageContext();

    // const isOpen = hash.startsWith("#search");
    const assistantFullScreen = location.pathname === SEARCH_PATH;
    const isOpen = assistantFullScreen || assistantSidebarOpen

    const closeSearch = useCallback(() => {
        if (isOpen) {
            // setHash();

        }
    }, [isOpen, /*setHash*/navigateState]);

    const openSearch = useCallback(() => {
        if (!isOpen) {
            setAssistantSidebarOpen(true);
        }
    }, [isOpen, /*setHash*/navigateState, setAssistantSidebarOpen]);

    return {
        searchIsOpen: isOpen,
        openSearch,
        assistantFullScreen,
        closeSearch,
    }
}

export function useHash(): [string, (hash?: string) => void] {
    const location = useLocation();
    const navigate = useNavigateWithPluginState();

    const setHash = useCallback((hash?: string) => navigate({...location, hash}), [location, navigate]);

    return [location.hash, setHash]
}

export function useSampleQueryParams(params?: GetSamplesRequest): GetSamplesRequest {
    const getFloatQueryParam = useGetFloatQueryParam();
    const getNumberQueryParam = useGetNumberQueryParam();
    const getFlagQueryParam = useGetFlagQueryParam();
    const getBooleanQueryParam = useGetBooleanQueryParam();
    const getArrayQueryParam = useGetArrayQueryParam();
    const getQueryParam = useGetQueryParam();
    const filterMinBpm = getFloatQueryParam("bpmMin");
    const filterMaxBpm = getFloatQueryParam("bpmMax");
    const filterLoop = (filterMaxBpm ?? filterMinBpm) !== undefined || getBooleanQueryParam("loop")

    const q = getQueryParam("q");
    const description = getQueryParam("d");

    return {
        liked: useLikedParam(),
        free: getFlagQueryParam("free"),
        royaltyFree: getFlagQueryParam("royalty-free"),
        ownerVerified: getFlagQueryParam("verified"),
        ownerFeatured: getFlagQueryParam("featured"),
        aiGenerated: getBooleanQueryParam("ai"),
        loop: filterLoop,
        bpmMin: filterMinBpm,
        bpmMax: filterMaxBpm,
        modelId: getNumberQueryParam("model"),
        durationMin: getFloatQueryParam("durationMin"),
        durationMax: getFloatQueryParam("durationMax"),
        tag: getArrayQueryParam("tag"),
        negativeTag: getArrayQueryParam("negativeTag"),
        ownerFollowed: useFollowedFlag(),
        description: description,
        scale: getQueryParam("scale"),
        artist: getNumberQueryParam("artist"),
        queryString: q,
        nameRegex: getQueryParam("regex"),
        sort: getArrayQueryParam<GetSamplesSortEnum>("sort"),
        ...params
    };
}

export const EXPLORE_SORT_OPTIONS = ["random", "latest", "popular", "recent"] as const
export const GetKitsSortEnumOptions = Object.values(GetKitsSortEnum) as GetKitsSortEnum[];
export type ExploreSort = typeof EXPLORE_SORT_OPTIONS[number];

export type ExploreQueryParams = {
    queryString?: string;
    followed?: boolean;
    liked?: boolean;
    free?: boolean;
    royaltyFree?: boolean;
    ownerVerified?: boolean;
    ownerFeatured?: boolean;

    minTempo?: number;
    maxTempo?: number;
    minDuration?: number;
    maxDuration?: number;

    tags?: string[];
    sort?: ExploreSort

    ownerId?: UserId;

    /**
     * TODO: send explore as a state param. might require refactor LinkSTate to suppoort object to
     */
    explore?: boolean;
};

function useFollowedFlag() {
    const isLoggedIn = useIsLoggedIn();
    const getFlagQueryParam = useGetFlagQueryParam();
    return isLoggedIn ? getFlagQueryParam("followed") : undefined;
}

export function useExploreQueryParams(params?: ExploreQueryParams): ExploreQueryParams {
    const getFlagQueryParam = useGetFlagQueryParam();
    const getArrayQueryParam = useGetArrayQueryParam();
    const getQueryParam = useGetQueryParam();
    const getNumberQueryParam = useGetNumberQueryParam();

    const sortParam = getQueryParam("sort");
    let sort: ExploreSort | undefined;
    if (isOneOf(sortParam, ...EXPLORE_SORT_OPTIONS)) {
        sort = sortParam;
    }

    return {
        queryString: getQueryParam("q"),
        liked: useLikedParam(),
        // free: getFlagQueryParam("free"),
        followed: useFollowedFlag(),
        explore: getFlagQueryParam("explore"),
        // royaltyFree: getFlagQueryParam("royalty-free"),
        // ownerVerified: getFlagQueryParam("verified"),
        // ownerFeatured: getFlagQueryParam("featured"),
        // minTempo: getNumberQueryParam("minTempo"),
        // maxTempo: getNumberQueryParam("maxTempo"),
        // minDuration: getNumberQueryParam("minDuration"),
        // maxDuration: getNumberQueryParam("maxDuration"),
        sort,
        ...params,
        ...baseExploreQueryParams,
        // tags: mergeTags(getArrayQueryParam("tag"), params?.tags)
    }
}

type QueryParamsValue = string | number | boolean | (string | number)[];
export type QueryParams = Record<string, QueryParamsValue> | any

export function isQueryFiltered(queryParams: QueryParams, base: QueryParams = {}) {
    const isNullish = (value?: QueryParamsValue) =>
        value === undefined ||
        value === null ||
        Array.isArray(value) && value.length === 0

    // console.trace("isQueryFiltered", queryParams, base);

    for (const key in queryParams) {
        const value = queryParams[key];
        const nullish = isNullish(value);

        const baseValue = base[key];
        const baseNullish = isNullish(baseValue);

        if (nullish !== baseNullish) {
            // console.log(`nullish mismatch for ${key}: ${nullish} !== ${baseNullish}`);
            return true;
        }

        if (nullish)
            continue;

        if (Array.isArray(value)) {
            if (!Array.isArray(baseValue)) {
                // console.log(`array mismatch for ${key}: ${value} !== ${baseValue}`);
                return true;
            }

            if (value.length !== baseValue.length) {
                // console.log(`array length mismatch for ${key}: ${value.length} !== ${baseValue.length}`);
                return true;
            }

            if (value.some((v, i) => v !== baseValue[i])) {
                // console.log(`array value mismatch for ${key}: ${value} !== ${baseValue}`);
                return true;
            }

        } else if (value !== baseValue) {
            // console.log(`value mismatch for ${key}: ${value} !== ${baseValue}`);
            return true;
        }
    }

    // console.log("no mismatches - not filtered");
    return false;
}

export function asSampleParams(queryParams: ExploreQueryParams): GetSamplesRequest {
    const ownerFeatured = queryParams.ownerFeatured ?? (!queryParams.ownerId && queryParams.sort === "latest" ? true : undefined);

    return {
        liked: queryParams.liked,
        free: queryParams.free,
        royaltyFree: queryParams.royaltyFree,
        ownerVerified: queryParams.ownerVerified,
        ownerFeatured,
        tag: queryParams.tags,
        queryString: queryParams.queryString,
        sort: queryParams.sort ? [queryParams.sort === "recent" ? GetSamplesSortEnum.RecentlyUsed : queryParams.sort] : undefined,

        bpmMin: queryParams.minTempo,
        bpmMax: queryParams.maxTempo,
        durationMin: queryParams.minDuration,
        durationMax: queryParams.maxDuration,
        ownerFollowed: queryParams.followed,
        artist: queryParams.ownerId
    }
}

export function getExplorePath(queryParams: ExploreQueryParams) {
    if (!canExploreAudio) {
        const query = asModelParams(queryParams);
        return getModelsPath(query ?? {});
    }

    const params = new URLSearchParams();

    if (queryParams.queryString)
        params.append("q", queryParams.queryString);

    if (queryParams.liked)
        params.append("liked", "true");

    if (queryParams.free)
        params.append("free", "true");

    if (queryParams.royaltyFree)
        params.append("royalty-free", "true");

    if (queryParams.ownerVerified)
        params.append("verified", "true");

    if (queryParams.ownerFeatured)
        params.append("featured", "true");

    if (queryParams.tags && queryParams.tags.length > 0)
        params.append("tag", queryParams.tags.join(","));

    if (queryParams.sort)
        params.append("sort", queryParams.sort);
    if (queryParams.minTempo)
        params.append("minTempo", queryParams.minTempo.toString());
    if (queryParams.maxTempo)
        params.append("maxTempo", queryParams.maxTempo.toString());
    if (queryParams.minDuration)
        params.append("minDuration", queryParams.minDuration.toString());
    if (queryParams.maxDuration)
        params.append("maxDuration", queryParams.maxDuration.toString());

    if (queryParams.followed)
        params.append("followed", "true");

    if (queryParams.explore)
        params.append("explore", "true");

    const base = queryParams.ownerId ? `/artist/${queryParams.ownerId}` : exploreRoot;

    return `${base}?${params}`
}

export function asModelParams(queryParams: ExploreQueryParams): ModelGetRequest | null {
    if (
        queryParams.minTempo !== undefined ||
        queryParams.maxTempo !== undefined ||
        queryParams.minDuration !== undefined ||
        queryParams.maxDuration !== undefined
    ) {
        return null;
    }

    return {
        liked: queryParams.liked,
        free: queryParams.free,
        royaltyFree: queryParams.royaltyFree,
        ownerVerified: queryParams.ownerVerified,
        ownerFeatured: queryParams.ownerFeatured,
        ownerFollowed: queryParams.followed,
        withArt: queryParams.sort === "latest" ? true : undefined,
        searchQuery: queryParams.queryString,
        sortStrategy: queryParams.sort ? [queryParams.sort === "recent" ? ModelSort.RecentlyUsed : queryParams.sort] : undefined,
        ownerId: queryParams.ownerId,
        tags: queryParams.tags
    }
}

export function asPacksParams(queryParams: ExploreQueryParams): GetKitsRequest | null {
    if (queryParams.sort === "recent" ||
        queryParams.liked !== undefined ||
        queryParams.free !== undefined ||
        queryParams.royaltyFree !== undefined ||
        queryParams.tags ||
        queryParams.minTempo !== undefined ||
        queryParams.maxTempo !== undefined ||
        queryParams.minDuration !== undefined ||
        queryParams.maxDuration !== undefined
    ) {
        return null
    }

    return {
        q: queryParams.queryString,
        ownerVerified: queryParams.ownerVerified,
        ownerFeatured: queryParams.ownerFeatured,
        ownerFollowed: queryParams.followed,
        sort: queryParams.sort,
        ownerId: queryParams.ownerId
    }
}

export function samplesAsPacksParams(queryParams: GetSamplesRequest): GetKitsRequest | null {
    const sort = queryParams.sort;
    const sortElement = sort?.[0];
    if (sort?.length !== 1 ||
        !isOneOf(sortElement, ...GetKitsSortEnumOptions) ||
        queryParams.liked !== undefined ||
        queryParams.free !== undefined ||
        queryParams.royaltyFree !== undefined ||
        queryParams.tag ||
        queryParams.bpmMin !== undefined ||
        queryParams.bpmMax !== undefined ||
        queryParams.durationMin !== undefined ||
        queryParams.durationMax !== undefined ||
        queryParams.modelId !== undefined ||
        queryParams.negativeTag !== undefined ||
        queryParams.ownerFollowed !== undefined ||
        queryParams.aiGenerated !== undefined ||
        queryParams.audioImportId !== undefined ||
        queryParams.audioId !== undefined ||
        queryParams.scale !== undefined ||
        queryParams.folderId !== undefined ||
        queryParams.parentFolderId !== undefined ||
        queryParams.likedByUserId !== undefined ||
        queryParams.single !== undefined ||
        queryParams.nameRegex !== undefined ||
        queryParams.description !== undefined ||
        queryParams._public !== undefined
    ) {
        return null
    }

    return {
        q: queryParams.queryString,
        ownerVerified: queryParams.ownerVerified,
        ownerFeatured: queryParams.ownerFeatured,
        ownerFollowed: queryParams.ownerFollowed,
        // public: queryParams._public, // TODO:
        withArt: queryParams.withArt,
        sort: sortElement,
        // parentFolderId: queryParams.parentFolderId,
        // audioImportId: queryParams.audioImportId, // TODO: allow this import filter (folders imported from a zip/directory)

        ownerId: queryParams.artist
    }
}

export function asProjectParams(queryParams: ExploreQueryParams): GetProjectsRequest | null {
    if (queryParams.liked || queryParams.free !== undefined || queryParams.royaltyFree !== undefined || queryParams.tags || queryParams.ownerVerified !== undefined || queryParams.ownerFeatured !== undefined) {
        return null
    }

    return {
        name: queryParams.queryString,
        sort: queryParams.sort === "recent" ? GetProjectsSortEnum.Updated :
            queryParams.sort === "latest" ? GetProjectsSortEnum.Newest :
                queryParams.sort,
        followed: queryParams.followed,
        minTempo: queryParams.minTempo,
        maxTempo: queryParams.maxTempo,
        minDuration: queryParams.minDuration,
        maxDuration: queryParams.maxDuration,
        ownerId: queryParams.ownerId,
    }
}

export function asArtistParams(queryParams: ExploreQueryParams): QueryUsersRequest | null {
    // if any other query params besides Q, return null
    if (queryParams.free !== undefined ||
        queryParams.royaltyFree !== undefined ||
        queryParams.tags?.length ||
        (queryParams.sort !== undefined && queryParams.sort !== "popular" /*TODO: use /featured path instead of (followed:true,sort:latest, solving this issue)*/) ||
        queryParams.minTempo !== undefined ||
        queryParams.maxTempo !== undefined ||
        queryParams.ownerId !== undefined ||
        queryParams.minDuration !== undefined ||
        queryParams.maxDuration !== undefined) {
        return null;
    }

    return {
        q: queryParams.queryString,
        featured: queryParams.ownerFeatured,
        verified: queryParams.ownerVerified,
        followed: queryParams.liked || queryParams.followed
    };
}


export function useDefaultSampleSort(): GetSamplesSortEnum[] {
    // const {userId} = useAuthContext();
    // return [userId === null ? GetSamplesSortEnum.Popular : GetSamplesSortEnum.ForYou];
    return [GetSamplesSortEnum.Popular];
}

function useLikedParam() {
    const getFlagQueryParam = useGetFlagQueryParam();
    return useIsLoggedIn() ? getFlagQueryParam("liked") : undefined;
}

export function useModelQueryParams(params?: ModelGetRequest): ModelGetRequest {
    const getFlagQueryParam = useGetFlagQueryParam();
    const getNumberQueryParam = useGetNumberQueryParam();
    const getArrayQueryParam = useGetArrayQueryParam();
    const getQueryParam = useGetQueryParam();

    return {
        liked: useLikedParam(),
        free: getFlagQueryParam("free"),
        royaltyFree: getFlagQueryParam("royalty-free"),
        ownerVerified: getFlagQueryParam("verified"),
        ownerFeatured: getFlagQueryParam("featured"),
        acceptsTextInput: getFlagQueryParam("text"),
        acceptsAudioInput: getFlagQueryParam("audio"),
        ownerId: getNumberQueryParam("artist"),
        negativeTags: getArrayQueryParam("negativeTag"),
        searchQuery: getQueryParam("q"),
        sortStrategy: getArrayQueryParam<ModelSort>("sort"),
        ownerFollowed: useFollowedFlag(),
        parentModelId: getNumberQueryParam("parentModel"),
        ...params,
        tags: mergeTags(getArrayQueryParam("tag"), params?.tags)
    };
}

function getModelSearchParams(query: ModelGetRequest) {
    const params = new URLSearchParams();
    if (query.tags && query.tags.length > 0)
        params.append("tag", query.tags.join(","));
    if (query.liked)
        params.append("liked", "true");
    if (query.free)
        params.append("free", "true");
    if (query.royaltyFree)
        params.append("royalty-free", "true");
    if (query.ownerVerified)
        params.append("verified", "true");
    if (query.ownerFeatured)
        params.append("featured", "true");
    if (query.acceptsTextInput)
        params.append("text", "true");
    if (query.acceptsAudioInput)
        params.append("audio", "true");
    if (query.searchQuery)
        params.append("q", query.searchQuery);
    if (query.sortStrategy)
        params.append("sort", query.sortStrategy.join(","));
    if (query.ownerFollowed)
        params.append("followed", "true");
    if (query.parentModelId)
        params.append("parentModel", query.parentModelId.toString());

    return params;
}

export function useProjectQueryParams(params?: GetProjectsRequest): GetProjectsRequest {
    const getNumberQueryParam = useGetNumberQueryParam();
    const getQueryParam = useGetQueryParam();
    const getBooleanQueryParam = useGetBooleanQueryParam();

    return {
        minTempo: getNumberQueryParam("minTempo"),
        maxTempo: getNumberQueryParam("maxTempo"),
        minDuration: getNumberQueryParam("minDuration"),
        maxDuration: getNumberQueryParam("maxDuration"),
        sort: getQueryParam("sort") as GetProjectsSortEnum,
        ownerId: getNumberQueryParam("ownerId"),
        _public: getBooleanQueryParam("public"),
        followed: useFollowedFlag(),
        name: getQueryParam("q"),
        limit: getNumberQueryParam("limit"),
        offset: getNumberQueryParam("offset"),
        ...params
    }
}

function getProjectSearchParams(query: GetProjectsRequest) {
    const params = new URLSearchParams();
    if (query.name)
        params.append("q", query.name);
    if (query.sort)
        params.append("sort", query.sort);
    if (query.ownerId)
        params.append("ownerId", query.ownerId.toString());
    if (query._public)
        params.append("public", "true");
    if (query.minTempo)
        params.append("minTempo", query.minTempo.toString());
    if (query.maxTempo)
        params.append("maxTempo", query.maxTempo.toString());
    if (query.minDuration)
        params.append("minDuration", query.minDuration.toString());
    if (query.maxDuration)
        params.append("maxDuration", query.maxDuration.toString());
    if (query.followed)
        params.append("followed", "true");

    return params;
}

export function getProjectQueryDependencies(query: GetProjectsRequest) {
    return [
        query.minTempo,
        query.maxTempo,
        query.minDuration,
        query.maxDuration,
        query.sort,
        query.ownerId,
        query._public,
        query.name,
        query.followed
    ];
}

export function getArtistProjectQueryDependencies(query: QueryUsersRequest) {
    return [
        query.q,
        query.verified,
        query.featured,
        query.followed
    ];
}

export function getExploreQueryDependencies(query: ExploreQueryParams) {
    return [
        query.queryString,
        query.liked,
        query.free,
        query.royaltyFree,
        query.ownerVerified,
        query.ownerFeatured,
        query.minTempo,
        query.maxTempo,
        query.minDuration,
        query.maxDuration,
        query.tags?.join(","),
        query.sort,
        query.followed
    ];
}

export function isProjectFiltered(queryParams: GetProjectsRequest) {
    return queryParams.minTempo !== undefined ||
        queryParams.maxTempo !== undefined ||
        queryParams.minDuration !== undefined ||
        queryParams.maxDuration !== undefined ||
        queryParams.sort !== undefined ||
        queryParams.ownerId !== undefined ||
        queryParams._public !== undefined ||
        queryParams.name !== undefined ||
        queryParams.followed !== undefined
}

export function getModelsPath(query: ModelGetRequest) {
    const base = query.ownerId ? `/artist/${query.ownerId}/models` : modelRoot;
    return `${base}?${getModelSearchParams(query)}`;
}

export function getProjectsPath(query: GetProjectsRequest) {
    return `/studio?${getProjectSearchParams(query)}`;
}

export function usePacksQueryParams(params?: GetKitsRequest): GetKitsRequest {
    const sort = usePacksSort();
    const getNumberQueryParam = useGetNumberQueryParam();
    const getFlagQueryParam = useGetFlagQueryParam();
    const getQueryParam = useGetQueryParam();

    return {
        q: getQueryParam("q"),
        nameRegex: getQueryParam("regex"),
        ownerId: getNumberQueryParam("artist"),
        ownerVerified: getFlagQueryParam("verified"),
        ownerFeatured: getFlagQueryParam("featured"),
        ownerFollowed: useFollowedFlag(),
        sort,
        ...params
    }
}

export function getPacksSearchParams(packsQuery: GetKitsRequest) {
    const params = new URLSearchParams();
    if (packsQuery.q)
        params.append("q", packsQuery.q);
    if (packsQuery.nameRegex)
        params.append("regex", packsQuery.nameRegex);
    if (packsQuery.ownerId)
        params.append("artist", packsQuery.ownerId.toString());
    if (packsQuery.ownerVerified)
        params.append("verified", "true");
    if (packsQuery.ownerFeatured)
        params.append("featured", "true");
    if (packsQuery.sort)
        params.append("sort", packsQuery.sort.toLowerCase());
    if (packsQuery.ownerFollowed)
        params.append("followed", "true");

    return params;
}

export function getPacksPath(packsQuery: GetKitsRequest) {
    const base = packsQuery.ownerId ?
        `artist/${packsQuery.ownerId}/packs` :
        "packs";

    return `/${base}?${(getPacksSearchParams(packsQuery))}`
}

export function isModelsFiltered(queryParams: ModelGetRequest) {
    return isQueryFiltered(queryParams, baseModelQueryParams);
}

export function isSamplesFiltered(queryParams: GetSamplesRequest) {
    return isQueryFiltered(queryParams, baseSampleQueryParams);
}

export function isPacksFiltered(queryParams: GetKitsRequest) {
    return queryParams.q !== undefined ||
        queryParams.nameRegex !== undefined ||
        queryParams.ownerId !== undefined ||
        queryParams.sort !== undefined ||
        queryParams.ownerVerified !== undefined ||
        queryParams.ownerFollowed !== undefined ||
        queryParams.ownerFeatured !== undefined
}

export function useParamMethods() {
    const location = useLocation();
    const [searchParams] = useSearchParams();

    const updateParams = useCallback((updater: (nextParams: URLSearchParams) => void) => {
        const newParams = new URLSearchParams(searchParams);
        updater(newParams)
        return location.pathname + "?" + newParams.toString();
    }, [searchParams, location.pathname]);

    const setParam = useCallback((key: string, value: any) => updateParams(nextParams => nextParams.set(key, value.toString())), [updateParams]);

    const clearFilter = useCallback((key: string) => updateParams(nextParams => nextParams.delete(key)), [updateParams]);

    const updateFlag = useCallback((key: string, value: boolean) => value ? setParam(key, true) : clearFilter(key), [setParam, clearFilter]);
    const updateBoolean = useCallback((key: string, value?: boolean) => value === undefined ? clearFilter(key) : setParam(key, value), [setParam, clearFilter]);

    const appendParam = useCallback((key: string, value: any) => updateParams(nextParams => {
        const current = nextParams.get(key);
        nextParams.set(key, current === null ? value.toString() : current + "," + value);
    }), [updateParams]);

    const appendParamUnique = useCallback((key: string, value: any) => updateParams(nextParams => {
        const current = nextParams.get(key);
        if (current === null) {
            nextParams.set(key, value.toString());
            return;
        }
        const values = current.split(",");
        if (values.includes(value)) return;
        values.push(value);
        nextParams.set(key, values.join(","));
    }), [updateParams]);

    const spliceParam = useCallback((key: string, value: string) => updateParams(nextParams => {
        const current = nextParams.get(key);
        if (current === null) return;
        const values = current.split(",");
        const index = values.indexOf(value);
        if (index === -1) return;
        values.splice(index, 1);
        if (values.length === 0) {
            nextParams.delete(key);
            return;
        }
        nextParams.set(key, values.join(","));
    }), [updateParams]);

    return {
        updateParams,
        setParam,
        clearFilter,
        updateFlag,
        updateBoolean,
        appendParam,
        appendParamUnique,
        spliceParam
    }
}

export function SearchInputComponent(props: {
    className?: string,
    param?: string,
    icon?: IconProp,
    placeholder?: string,
}) {
    const navigate = useNavigateWithPluginState();
    const {setParam, clearFilter} = useParamMethods();
    
    const paramName = props.param || "q";
    const value = useQueryParams().get(paramName);

    const filtered = !!value;
    const className = classNames(
        "button !rounded-full h-8 px-3",
        filtered ? "bg-white text-black" : "bg-lighten hoverable",
        props.className,
    );

    if (!filtered) {
        return <SearchInput className={className} 
                           param={paramName}
                           icon={props.icon}
                           placeholder={props.placeholder}/>
    }

    return <SearchValueComponentRaw className={className}
                                    icon={props.icon}
                                    placeholder={props.placeholder}
                                    onChange={queryString => {
                                        console.log("updating search", queryString);
                                        navigate(setParam(paramName, queryString))
                                    }}
                                    onClear={() => {
                                        console.log("clearing search");
                                        navigate(clearFilter(paramName))
                                    }}
                                    value={value}/>
}

export function SearchInput(props: {
    className?: string,
    param?: string,
    icon?: IconProp,
    placeholder?: string,
}) {
    const {setParam} = useParamMethods();
    const navigate = useNavigateWithPluginState();
    const paramName = props.param || "q";

    return <AddInput onSubmit={queryString => {
        if (!queryString) return;
        navigate(setParam(paramName, queryString))
    }}
                     className={props.className}
        // buttonClass={null}
                     left
                     icon={props.icon ? <Icon icon={props.icon} size={18}/> : "search"}
                     placeholder={props.placeholder || "Search"}/>
}

export function SearchInputCallback(props: {
    update: (value: string) => void,
    className?: string,
}) {
    return <AddInput
        onSubmit={queryString => {
            if (!queryString) return;
            props.update(queryString);
        }}
        className={props.className}
        left
        icon="search"
        placeholder="Search"/>
}

export function FreeTierToggle() {
    const isFreeTier = useIsFreeTier();

    if (isFreeTier)
        return <SearchFlagToggle name="free"
                                 icon="shop">
            free Tier
        </SearchFlagToggle>

    return null;
}

export function FreeTierToggleCallback(props: {
    params: GetSamplesRequest,
    update: (params: GetSamplesRequest) => void,
}) {
    const isFreeTier = useIsFreeTier();

    if (isFreeTier)
        return <SearchFlagToggleCallback icon="shop"
                                         title="Free Tier"
                                         update={value => props.update({
                                             ...props.params,
                                             free: value ? true : undefined
                                         })}
                                         value={props.params.free ?? false}>
            Free Tier
        </SearchFlagToggleCallback>

    return null;

}

export function makeFilterButtonClassname(active?: boolean) {
    return classNames("button !rounded-full h-8 px-3", active ? "bg-white text-black hoverable-white" : "bg-lighten hoverable");
}

export function SearchFlagToggle(props: {
    children: ReactNode,
    icon?: string,
    title?: string,
    name: string,
    hidden?: boolean
}) {
    const {updateFlag} = useParamMethods();
    const getFlagQueryParam = useGetFlagQueryParam();
    const value = getFlagQueryParam(props.name);

    if (!value && props.hidden) {
        return null
    }

    return <LinkState
        title={props.title}
        className={makeFilterButtonClassname(value)}
        to={updateFlag(props.name, !value)}>
        {props.icon && <MaterialIcon icon={props.icon} fill opticalSize={20}/>}
        {props.children}
    </LinkState>;
}

export function SearchTempToggle(props: {
    children: ReactNode,
    icon?: string,
    title?: string,
    name: string,
}) {
    const {updateFlag, clearFilter} = useParamMethods();
    const getQueryParam = useGetQueryParam();
    const value = getQueryParam(props.name);
    const hasValue = value !== undefined;

    if (!hasValue) {
        return null
    }

    return <LinkState
        title={props.title}
        className={makeFilterButtonClassname(hasValue)}
        to={clearFilter(props.name)}>
        {props.icon && <MaterialIcon icon={props.icon} fill opticalSize={20}/>}
        {props.children}
    </LinkState>;
}

export function SearchFlagToggleCallback(props: {
    children: ReactNode,
    icon?: IconProp,
    title?: string,
    update: (value?: boolean) => void,
    value?: boolean
}) {
    return <button
        title={props.title}
        className={makeFilterButtonClassname(props.value)}
        onClick={() => props.update(props.value ? undefined : true)}>
        <Icon icon={props.icon}
              fill
              opticalSize={20}/>
        {props.children}
    </button>;
}

export function RoyaltyFreeToggle() {
    return <SearchFlagToggle name="royalty-free"
                             icon="contract_delete">
        Royalty-Free
    </SearchFlagToggle>;
}

export function HideAIToggle() {
    const {updateBoolean} = useParamMethods();
    const getBooleanQueryParam = useGetBooleanQueryParam();
    const value = getBooleanQueryParam("ai") === false;

    return <LinkState to={updateBoolean("ai", value ? undefined : false)}
                      title={value ? "Include samples made with AI in results" : "Hide samples made with AI from results"}
                      className={makeFilterButtonClassname(value)}>
        <Icon icon={AiIcon}/>
        Hide AI
    </LinkState>;
}

export function HideAIToggleCallback(props: {
    params: GetSamplesRequest,
    update: (params: GetSamplesRequest) => void,
}) {
    const value = props.params.aiGenerated === false;
    return <SearchFlagToggleCallback icon={AiIcon}
                                     title={value ? "Include samples made with AI in results" : "Hide samples made with AI from results"}
                                     update={value => props.update({
                                         ...props.params,
                                         aiGenerated: value ? false : undefined
                                     })}
                                     value={value}>
        Hide AI
    </SearchFlagToggleCallback>;
}

export function RoyaltyFreeToggleCallback(props: {
    params: GetSamplesRequest,
    update: (params: GetSamplesRequest) => void,
}) {
    return <SearchFlagToggleCallback icon="contract_delete"
                                     title="Royalty-Free"
                                     update={value => props.update({
                                         ...props.params,
                                         royaltyFree: value ? true : undefined
                                     })}
                                     value={props.params.royaltyFree}>
        Royalty-Free
    </SearchFlagToggleCallback>;
}

export function LikedToggle() {
    const isLoggedIn = useIsLoggedIn();

    if (isLoggedIn)
        return <SearchFlagToggle name="liked" icon="favorite">
            Liked
        </SearchFlagToggle>;
}

export function LikedToggleCallback(props: {
    params: { liked?: boolean },
    update: (params: { liked?: boolean }) => void,
}) {
    const isLoggedIn = useIsLoggedIn();

    if (isLoggedIn)
        return <SearchFlagToggleCallback icon="favorite"
                                         title="Liked"
                                         update={value => props.update({
                                             ...props.params,
                                             liked: value ? true : undefined
                                         })}
                                         value={props.params.liked}>
            Liked
        </SearchFlagToggleCallback>;
}

export function FeaturedToggle(props: {
    alwaysShow?: boolean
}) {
    return <>
        <SearchFlagToggle name="featured"
                          hidden={!props.alwaysShow}
                          icon="star">
            Featured
        </SearchFlagToggle>
        <SearchFlagToggle name="verified"
                          hidden
                          icon="verified">
            Verified
        </SearchFlagToggle>
    </>;
}

export function FollowedToggle() {
    const isLoggedIn = useIsLoggedIn();

    if (!isLoggedIn)
        return null

    return <SearchFlagToggle name="followed" icon="person">
        followed
    </SearchFlagToggle>;
}


export function getArtistQueryDescription(queryParams: QueryUsersRequest): string {
    const {q, featured, verified, followed} = queryParams;
    let description = "";

    // Only include one of "Featured" or "Verified":
    // If ownerFeatured is true, then use it. Otherwise, if ownerVerified is true, then use that.
    if (featured) {
        description += "Featured ";
    } else if (verified) {
        description += "Verified ";
    }

    // Append the base subject.
    description += "Artists";

    // Append "Followed" if the followed flag is true.
    if (followed) {
        description += " you follow";
    }

    // Append a matching clause if a query string is provided.
    const trimmedQuery = q?.trim();
    if (trimmedQuery?.length) {
        description += ` matching "${trimmedQuery}"`;
    }

    return description.trim();
}

export function getArtistPath(queryParams: QueryUsersRequest) {
    const params = new URLSearchParams();

    if (queryParams.q)
        params.append("q", queryParams.q);
    if (queryParams.featured)
        params.append("featured", "true");
    if (queryParams.verified)
        params.append("verified", "true");
    if (queryParams.followed)
        params.append("followed", "true");

    return `/artists?${params}`
}

/**
 * Builds a human‑friendly description of the sample packs query.
 *
 * Examples:
 * - "Latest Featured Sample Packs with Art matching "drums""
 * - "Popular Sample Packs matching "vintage""
 * - "Oldest Verified Sample Packs"
 *
 * @param queryParams - The parameters used to query sample packs.
 * @returns A short description of the query.
 */
export function getPacksQueryDescription(queryParams: GetKitsRequest): string {
    const {q, sort, ownerFeatured, ownerVerified, withArt} = queryParams;
    let description = "";

    if (sort) {
        if (sort === GetKitsSortEnum.Latest) {
            description += "New ";
        } else if (sort === GetKitsSortEnum.Oldest) {
            description += "Old ";
        } else if (sort === GetKitsSortEnum.Popular) {
            description += "Popular ";
        } else if (sort === GetKitsSortEnum.Random && !ownerFeatured) {
            description += "Random ";
        }
    }

    if (ownerFeatured) {
        description += "Featured ";
    }

    description += "Packs";

    if (q && q.trim().length > 0) {
        description += ` matching "${q.trim()}"`;
    }

    // Append a phrase if filtering by followed artists
    if (queryParams.ownerFollowed) {
        description += " from people you follow";
    }

    return description.trim();
}

export function useArtistQueryParams(): QueryUsersRequest {
    const getQueryParam = useGetQueryParam();
    const getFlagQueryParam = useGetFlagQueryParam();

    return {
        q: getQueryParam("q"),
        featured: getFlagQueryParam("featured"),
        verified: getFlagQueryParam("verified"),
        followed: useFollowedFlag(),
    };
}

export function getPackQueryDependencies(query: GetKitsRequest) {
    return [
        query.q,
        query.ownerId,
        query.ownerVerified,
        query.sort,
        query.ownerFeatured,
        query.ownerFollowed
    ];
}

const SPECIAL_TAGS = new Set([
    "speech",
    "stem",
    "extend",
    "tool",
])

export function getModelQueryDescription(params: ModelGetRequest) {
    let prompt = ""

    const {
        liked,
        acceptsTextInput,
        acceptsAudioInput,
        tags, royaltyFree, sortStrategy
    } = params;

    switch (sortStrategy?.[0]) {
        case "random":
            prompt += "Random "
            break;
        case "popular":
            prompt += "Popular "
            break;
        case "oldest":
            prompt += "Old "
            break;
        case "latest":
            prompt += "New "
            break;
        case "recently_used":
            prompt += "Recently used "
            break;
    }


    if (royaltyFree === true) {
        prompt += "royalty-free "
    } /*else if (negativeTag !== undefined) {
        prompt += "non-" + negativeTag[0] + " "
    }*/


    prompt += "AI "

    const isVoiceSwapping = tags && (tags.includes("vocal") || tags.includes("voice")) && acceptsAudioInput

    let visibleTag: string | undefined;
    if (tags && !isVoiceSwapping) {
        const visibleTags = tags.filter(t => !SPECIAL_TAGS.has(t))
        if (visibleTags.length > 0) {
            visibleTag = visibleTags[0];

            if (!acceptsTextInput)
                prompt += capitalize(visibleTag) + " "
        }
    }

    if (tags?.includes("stem")) {
        prompt += "Stem Splitters "
    } else {
        if (tags?.includes("extend")) {
            prompt += "Extension "
        } else if (params.acceptsAudioInput && !isVoiceSwapping) {
            prompt += "Audio Transform "
        } else if (acceptsTextInput === true) {
            if (tags?.includes("speech")) {
                prompt += "Text-to-Speech "
            } else {
                prompt += `Text-to-${visibleTag ?? "Audio"} `
            }
        }

        prompt += "tools"
    }

    if (liked) {
        prompt += " you've liked"
    } /*else {
        if (sortStrategy === "for_you") prompt += " for you"
    }*/ else if (params.ownerFollowed) {
        prompt += " from people you follow"
    }
    return prompt;
}

export function useGenerationJobDependencies(params: GetGenerationJobsRequest) {
    const {userId} = useAuthContext();

    return [userId, params.modelId, params.offset, params.limit];
}

export function getModelQueryDependencies(params: ModelGetRequest) {
    return [
        params.liked,
        params.tags?.sort().join(","),
        params.free,
        params.royaltyFree,
        params.sortStrategy?.join(","),
        params.offset,
        params.limit,
        params.searchQuery,
        params.acceptsAudioInput,
        params.acceptsTextInput,
        params.acceptsOutputCountInput,
        params.ownerFeatured,
        params.ownerVerified,
        params.ownerFollowed,
        params.parentModelId,
    ];
}

export function ResetFilterLink() {
    const location = useLocation();

    return <LinkState className="icon-button round hoverable bg-lighten h-8 -mr-0.5"
                      to={location.pathname}
                      title="Reset Filters">
        <CloseIcon/>
    </LinkState>;
}

export function ResetFilterButton(props: {
    onClick: () => void
}) {
    return <button className="icon-button round hoverable bg-lighten h-8 -mr-0.5"
                   onClick={props.onClick}
                   title="Reset Filters">
        <CloseIcon/>
    </button>;
}


/**
 * Reverses asSampleParams:
 * Converts a GetSamplesRequest back into an ExploreQueryParams.
 */
export function samplesAsExploreParams(request: GetSamplesRequest): ExploreQueryParams {
    let sort: ExploreSort | undefined;
    if (request.sort?.length) {
        const firstSort = request.sort[0];

        if (firstSort !== "oldest" && firstSort !== "most_used" && firstSort !== "for_you") {
            sort = firstSort === "recently_used" ? "recent" :
                isSimilarSort(firstSort) ? undefined :
                    firstSort;
        }
    }

    return {
        queryString: request.queryString,
        liked: request.liked,
        free: request.free,
        royaltyFree: request.royaltyFree,
        ownerVerified: request.ownerVerified,
        ownerFeatured: request.ownerFeatured,
        minTempo: request.bpmMin,
        maxTempo: request.bpmMax,
        minDuration: request.durationMin,
        maxDuration: request.durationMax,
        tags: request.tag,
        sort,
        followed: request.ownerFollowed,
        ownerId: request.artist as UserId,
    };
}

/**
 * Reverses asModelParams:
 * Converts a ModelGetRequest back into an ExploreQueryParams.
 * Note: if withArt was set (when sort was originally "latest") we try to reapply that.
 */
export function modelAsExploreParams(model: ModelGetRequest): ExploreQueryParams {
    return {
        queryString: model.searchQuery,
        liked: model.liked,
        free: model.free,
        royaltyFree: model.royaltyFree,
        ownerVerified: model.ownerVerified,
        ownerFeatured: model.ownerFeatured,
        tags: model.tags,
        sort:
            model.sortStrategy && model.sortStrategy.length > 0
                ? (model.sortStrategy[0] === ModelSort.RecentlyUsed
                    ? "recent"
                    : (model.sortStrategy[0].toLowerCase() as ExploreSort))
                : model.withArt
                    ? "latest"
                    : undefined,
        followed: model.ownerFollowed,
        ownerId: model.ownerId as UserId,
    };
}

/**
 * Reverses asPacksParams:
 * Converts a GetKitsRequest back into an ExploreQueryParams.
 */
export function packsAsExploreParams(packs: GetKitsRequest): ExploreQueryParams {
    return {
        queryString: packs.q,
        ownerVerified: packs.ownerVerified,
        ownerFeatured: packs.ownerFeatured,
        followed: packs.ownerFollowed,
        sort: packs.sort === "oldest" ? undefined : packs.sort, // For packs, the valid ExploreSort values are limited (e.g. not "recent")
        ownerId: packs.ownerId as UserId,
    };
}

/**
 * Reverses asProjectParams:
 * Converts a ProjectQueryParams back into an ExploreQueryParams.
 */
export function projectAsExploreParams(project: GetProjectsRequest): ExploreQueryParams {
    return {
        queryString: project.name,
        sort:
            project.sort === GetProjectsSortEnum.Updated
                ? "recent"
                : project.sort === GetProjectsSortEnum.Newest
                    ? "latest"
                    : (project.sort as ExploreSort),
        followed: project.followed,
        minTempo: project.minTempo,
        maxTempo: project.maxTempo,
        minDuration: project.minDuration,
        maxDuration: project.maxDuration,
        ownerId: project.ownerId as UserId,
    };
}

/**
 * Reverses asArtistParams:
 * Converts a QueryUsersRequest back into an ExploreQueryParams.
 * (Note that the original mapping merges “liked” and “followed”. Here we simply use the followed flag.)
 */
export function artistAsExploreParams(artist: QueryUsersRequest): ExploreQueryParams {
    return {
        queryString: artist.q,
        ownerFeatured: artist.featured,
        ownerVerified: artist.verified,
        followed: artist.followed,
        // The original asArtistParams combined liked and followed; if needed, you could also set liked.
        // liked: artist.followed, // or leave undefined
    };
}
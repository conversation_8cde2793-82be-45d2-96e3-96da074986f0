import React, {useEffect, useRef, useState} from "react";
import {MaterialIcon} from "./icons";
import "../assets/css/scroller.scss";
import {useSize} from "./Utils";
import classNames from "classnames";

export const VerticalScroller = React.forwardRef<HTMLDivElement, {
    children: React.ReactNode;
    hideButtons?: boolean;
    className?: string,
    contentClassName?: string;
    sideSpacing?: boolean,
    small?: boolean,
    fadeTopSize?: string,
    fadeBottomSize?: string
}>((props, ref) => {
    const scrollerRef = useRef<HTMLDivElement>(null)
    const [overflowing, setOverflowing] = useState({top: false, bottom: false});
    const size = useSize(scrollerRef);

    function updateOverflowing() {
        const isOverflowing = scrollerRef.current ?
            {
                top: scrollerRef.current.scrollTop > 1,
                bottom: scrollerRef.current.scrollTop + scrollerRef.current.clientHeight < scrollerRef.current.scrollHeight - 1
            } :
            {top: false, bottom: false};

        setOverflowing(isOverflowing);
    }

    useEffect(() => {
        updateOverflowing();
    }, [size?.height, props.children])

    return <div ref={ref}
                className={classNames(
                    "twoshot-vertical-scroller",
                    props.className,
                    {
                        small: props.small,
                        top: overflowing.top,
                        bottom: overflowing.bottom,
                        "auto-hide": props.hideButtons
                    }
                )}>
        <div className="top">
            <button
                className={classNames({
                    "pointer-events-none": props.hideButtons || !overflowing.top
                })}
                onClick={e => {
                    e.preventDefault();
                    e.stopPropagation();
                    if (scrollerRef.current)
                        scrollerRef.current.scrollBy({
                            top: -scrollerRef.current.clientHeight * 0.8,
                            behavior: "smooth"
                        })
                }}>
                <MaterialIcon icon="keyboard_arrow_up"/>
            </button>
        </div>
        <div className="bottom">
            <button
                className={classNames({
                    "pointer-events-none": props.hideButtons || !overflowing.bottom
                })}
                onClick={e => {
                e.preventDefault();
                e.stopPropagation();
                if (scrollerRef.current)
                    scrollerRef.current.scrollBy({
                        top: scrollerRef.current.clientHeight * 0.8,
                        behavior: "smooth"
                    })
            }}>
                <MaterialIcon icon="keyboard_arrow_down"/>
            </button>
        </div>
        <div className={classNames(
            "content",
            props.sideSpacing && "side-spacing-scroll",
            props.contentClassName
        )}
             ref={scrollerRef}
             onScroll={updateOverflowing}
             style={{
                 ...(props.fadeTopSize || props.fadeBottomSize ? {
                     '--fade-top-size': props.fadeTopSize || '40px',
                     '--fade-bottom-size': props.fadeBottomSize || '80px'
                 } as React.CSSProperties : {})
             }}>
            {props.children}
        </div>
    </div>
})